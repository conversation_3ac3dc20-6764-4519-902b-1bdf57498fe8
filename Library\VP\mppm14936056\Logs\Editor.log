[Licensing::Mo<PERSON><PERSON>] Trying to connect to existing licensing client channel...
Built from '6000.2/staging' branch; Version is '6000.2.6f1 (cc51a95c0300) revision 13390249'; Using compiler version '194234433'; Build Type 'Release'
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-camil" at "2025-10-05T23:14:22.4246469Z"
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'es' Physical Memory: 32598 MB
BatchMode: 0, IsHumanControllingUs: 1, StartBugReporterOnCrash: 1, Is64bit: 1
Date: 2025-10-05T23:14:22Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Unity.exe
-editor-mode
com.unity.mppm.clone
-no-cloud-project-bind-popup
-noLaunchScreen
-library-redirect
../..
-readonly
-noUpm
-upmRestorePackages
-name
Player 2
-projectPath
C:\Users\<USER>\TicTacToe\Library\VP\mppm14936056
--virtual-project-clone
-forgetProjectPath
-library-redirect
../..
-readonly
-DisableDirectoryMonitor
-noUpm
-upmRestorePackages
-noMainWindow
-suppressDefaultMenuEntries
-logFile
C:\Users\<USER>\TicTacToe\Library\VP\mppm14936056\Logs\Editor.log
-vp-channel-name=vp-channel
-ump-channel-service-port
50575
-mainProcessId=18664
-vpId=mppm14936056
-buildTarget
StandaloneWindows64
-standaloneBuildSubtarget
Player
Successfully changed project path to: C:\Users\<USER>\TicTacToe\Library\VP\mppm14936056
C:/Users/<USER>/TicTacToe/Library/VP/mppm14936056
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [17368]  Target information:

Player connection [17368]  * "[IP] *********** [Port] 55505 [Flags] 2 [Guid] 1585260958 [EditorId] 1585260958 [Version] 1048832 [Id] WindowsEditor(7,Camilo-PC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [17368]  * "[IP] *********** [Port] 55505 [Flags] 2 [Guid] 1585260958 [EditorId] 1585260958 [Version] 1048832 [Id] WindowsEditor(7,Camilo-PC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [17368] Host joined multi-casting on [***********:54997]...
Player connection [17368] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 7048, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.3+a4e98f1
  Session Id:              31c31b8547984967990cb331032880d6
  Correlation Id:          489e70f3de3d89558a8dab675c636732
  External correlation Id: 3795467156227208617
  Machine Id:              H/eV6RgZrlsD6oHPYwHyIJhNpFc=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-camil" (connect: 0.00s, validation: 0.00s, handshake: 0.03s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-camil-notifications" at "2025-10-05T23:14:22.4585074Z"
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: 4673220025021-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
[Licensing::Client] Successfully updated license, isAsync: True, time: 0.00
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Licensing Background thread has ended after 0.04s
Library Redirect Path: ../../
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Targeting platform: StandaloneWindows64
Refreshing native plugins compatible for Editor in 0.38 ms, found 0 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.6f1 (cc51a95c0300)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.2.6f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/TicTacToe/Library/VP/mppm14936056/Assets
GfxDevice: creating device client; kGfxThreadingModeSplitJobs
d3d12: failed to query info queue interface (0x80004002).
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:          NVIDIA
    VRAM:            16109 MB
    App VRAM Budget: 15341 MB
    Driver:          32.0.15.8115
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.2.6f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.2.6f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.2.6f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56448
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
ImportWorker Server TCP listen port: 0
AcceleratorClientConnectionCallback - disconnected - :0
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.6f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.6f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001883 seconds.
- Loaded All Assemblies, in  0.266 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.252 seconds
Domain Reload Profiling: 517ms
	BeginReloadAssembly (89ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (108ms)
		LoadAssemblies (87ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (106ms)
			TypeCache.Refresh (105ms)
				TypeCache.ScanAssembly (95ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (252ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (227ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (42ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (46ms)
			ProcessInitializeOnLoadAttributes (93ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
[Licensing::Client] Successfully resolved entitlement details
Application.AssetDatabase Initial Refresh Start
Package Manager log level set to [2]
[Package Manager] Restoring resolved packages state from cache
[Licensing::Client] Successfully resolved entitlement details
[Package Manager] Registered 69 packages:
  Packages from [https://packages.unity.com]:
    com.unity.2d.animation@12.0.2 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.animation@34e0443c58ed)
    com.unity.2d.aseprite@2.0.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.aseprite@f6e7e126ac6d)
    com.unity.2d.psdimporter@11.0.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.psdimporter@0adcab25a8fd)
    com.unity.2d.spriteshape@12.0.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.spriteshape@1d246726c231)
    com.unity.2d.tilemap.extras@5.0.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.tilemap.extras@2338d989ff2a)
    com.unity.collab-proxy@2.9.3 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.collab-proxy@ab839cc7d2ad)
    com.unity.ide.rider@3.0.38 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.ide.rider@1f60b3138684)
    com.unity.ide.visualstudio@2.0.23 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.ide.visualstudio@198cdf337d13)
    com.unity.inputsystem@1.14.2 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.inputsystem@be6c4fd0abf5)
    com.unity.multiplayer.playmode@1.6.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.multiplayer.playmode@3acaa705f0f9)
    com.unity.multiplayer.tools@2.2.6 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.multiplayer.tools@8395f16a9a9a)
    com.unity.netcode.gameobjects@2.5.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.netcode.gameobjects@60f197570e52)
    com.unity.timeline@1.8.9 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.timeline@6b9e48457ddb)
    com.unity.visualscripting@1.9.7 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.visualscripting@6279e2b7c485)
    com.unity.transport@2.5.3 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.transport@b61461f89a69)
    com.unity.nuget.mono-cecil@1.11.5 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.nuget.mono-cecil@d78732e851eb)
    com.unity.burst@1.8.24 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.burst@f7a407abf4d5)
    com.unity.collections@2.5.7 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.collections@d49facba0036)
    com.unity.mathematics@1.3.2 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.mathematics@8017b507cc74)
    com.unity.profiling.core@1.0.2 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.profiling.core@aac7b93912bc)
    com.unity.nuget.newtonsoft-json@3.2.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0)
    com.unity.2d.common@11.0.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.common@dd402daace1b)
    com.unity.searcher@4.9.3 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.searcher@1e17ce91558d)
    com.unity.test-framework.performance@3.1.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.test-framework.performance@92d1d09a72ed)
  Built-in packages:
    com.unity.2d.sprite@1.0.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.sprite@331abd9a1e2f)
    com.unity.2d.tilemap@1.0.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.tilemap@aa5f5491174d)
    com.unity.multiplayer.center@1.0.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.multiplayer.center@f3fb577b3546)
    com.unity.render-pipelines.universal@17.2.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.render-pipelines.universal@1e1527b3a17b)
    com.unity.test-framework@1.6.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.test-framework@40b8da2bd3e9)
    com.unity.ugui@2.0.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.ugui@e375ff18e90f)
    com.unity.modules.accessibility@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.accessibility)
    com.unity.modules.ai@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ai)
    com.unity.modules.androidjni@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.androidjni)
    com.unity.modules.animation@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.animation)
    com.unity.modules.assetbundle@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.assetbundle)
    com.unity.modules.audio@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.audio)
    com.unity.modules.cloth@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.cloth)
    com.unity.modules.director@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.director)
    com.unity.modules.imageconversion@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imageconversion)
    com.unity.modules.imgui@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imgui)
    com.unity.modules.jsonserialize@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.jsonserialize)
    com.unity.modules.particlesystem@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.particlesystem)
    com.unity.modules.physics@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics)
    com.unity.modules.physics2d@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics2d)
    com.unity.modules.screencapture@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.screencapture)
    com.unity.modules.terrain@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrain)
    com.unity.modules.terrainphysics@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrainphysics)
    com.unity.modules.tilemap@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.tilemap)
    com.unity.modules.ui@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ui)
    com.unity.modules.uielements@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.uielements)
    com.unity.modules.umbra@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.umbra)
    com.unity.modules.unityanalytics@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unityanalytics)
    com.unity.modules.unitywebrequest@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequest)
    com.unity.modules.unitywebrequestassetbundle@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestassetbundle)
    com.unity.modules.unitywebrequestaudio@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestaudio)
    com.unity.modules.unitywebrequesttexture@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequesttexture)
    com.unity.modules.unitywebrequestwww@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestwww)
    com.unity.modules.vehicles@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vehicles)
    com.unity.modules.video@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.video)
    com.unity.modules.vr@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vr)
    com.unity.modules.wind@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.wind)
    com.unity.modules.xr@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.xr)
    com.unity.modules.subsystems@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.subsystems)
    com.unity.modules.hierarchycore@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.hierarchycore)
    com.unity.ext.nunit@2.0.5 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.ext.nunit@031a54704bff)
    com.unity.render-pipelines.core@17.2.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.render-pipelines.core@317e801bb3aa)
    com.unity.shadergraph@17.2.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.shadergraph@5516e0d97518)
    com.unity.render-pipelines.universal-config@17.0.3 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.render-pipelines.universal-config@8dc1aab4af1d)
    com.unity.rendering.light-transport@1.0.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.rendering.light-transport@2c9279f90d7c)
[Subsystems] No new subsystems found in resolved package list.
[Package Manager] Done registering packages in 0.02 seconds
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.685 seconds
Refreshing native plugins compatible for Editor in 1.56 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.251 seconds
Domain Reload Profiling: 3931ms
	BeginReloadAssembly (179ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (58ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (20ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (438ms)
		LoadAssemblies (292ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (217ms)
			TypeCache.Refresh (151ms)
				TypeCache.ScanAssembly (139ms)
			BuildScriptInfoCaches (55ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (3251ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3128ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (2813ms)
			ProcessInitializeOnLoadMethodAttributes (159ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Asset Pipeline Refresh (id=84430f9749a11cd4da01525522296c35): Total: 4.473 seconds - Initiated by InitialRefreshV2(ForceSynchronousImport)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=3756 ms
		Asset DB Callback time: managed=0 ms, native=29 ms
		Scripting: domain reloads=1, domain reload time=686 ms, compile time=1 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeCustomDependenciesCallbacks: 0.000ms
	InvokePackagesCallback: 29.690ms
	ApplyChangesToAssetFolders: 0.225ms
	CategorizeAssets: 48.148ms
	ImportOutOfDateAssets: 3260.097ms (3257.417ms without children)
		CompileScripts: 0.950ms
		ReloadNativeAssets: 0.016ms
		UnloadImportedAssets: 0.743ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.595ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.375ms
	Hotreload: 1.243ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.003ms
	UnloadStreamsBegin: 0.190ms
	UnloadStreamsEnd: 0.001ms
	GenerateScriptTypeHashes: 2.164ms
	Untracked: 1133.601ms

Application.AssetDatabase Initial Refresh End
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Scanning for USB devices : 11.995ms
Initializing Unity extensions:
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-TicTacToe
[MODES] Loading editor mode Multiplayer Playmode Clone (1) from command line.
Unloading 56 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7971 unused Assets / (12.0 MB). Loaded Objects now: 8759.
Memory consumption went from 245.0 MB to 233.0 MB.
Total: 22.477500 ms (FindLiveObjects: 0.553900 ms CreateObjectMapping: 0.616400 ms MarkObjects: 16.612000 ms  DeleteObjects: 4.694200 ms)

[Project] Loading completed in 6.571 seconds
	Project init time: 				5.964 seconds
		Template init time: 		0.000 seconds
		Package Manager init time: 		0.000 seconds
		Asset Database init time: 		0.125 seconds
		Global illumination init time: 	0.002 seconds
		Assemblies load time: 			0.532 seconds
		Unity extensions init time: 	0.029 seconds
		Asset Database refresh time: 	4.473 seconds
	Scene opening time: 			0.546 seconds
##utp:{"type":"ProjectInfo","version":2,"phase":"Immediate","time":1759706068976,"processId":19448,"projectLoad":6.570545699999999,"projectInit":5.9643322,"templateInit":0.0,"packageManagerInit":6e-7,"assetDatabaseInit":0.1254609,"globalIlluminationInit":0.0019535,"assembliesLoad":0.5323416,"unityExtensionsInit":0.0294696,"assetDatabaseRefresh":4.4733143,"sceneOpening":0.5461954}
##utp:{"type":"EditorInfo","version":2,"phase":"Immediate","time":1759706068976,"processId":19448,"editorVersion":"6000.2.6f1 (cc51a95c0300)","branch":"6000.2/staging","buildType":"Release","platform":"Windows"}
Asset Pipeline Refresh (id=61eaf5e945a0c9d4ba664097a4f29cea): Total: 0.467 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=466 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=0, domain reload time=0 ms, compile time=0 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeCustomDependenciesCallbacks: 0.001ms
	InvokePackagesCallback: 0.001ms
	ApplyChangesToAssetFolders: 0.186ms
	CategorizeAssets: 62.029ms
	ImportOutOfDateAssets: 1.334ms (0.313ms without children)
		ReloadNativeAssets: 0.018ms
		UnloadImportedAssets: 0.001ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.662ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.339ms
	Hotreload: 1.000ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.187ms
	UnloadStreamsBegin: 1.146ms
	UnloadStreamsEnd: 0.001ms
	Untracked: 400.800ms

Attempted to open unsupported window in cloned player systems: CodeMonkey.FreeWindow.MainWindowFree.Please reach out on the forums and contact Unity Admins for more info.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
Unity.Multiplayer.Playmode.Workflow.Editor.ClonedPlayerSystems:BlockWindowIfRequired (UnityEditor.EditorWindow) (at ./Library/PackageCache/com.unity.multiplayer.playmode@3acaa705f0f9/Workflow/Editor/EditorSystems/ClonedPlayerSystems.cs:85)
Unity.Multiplayer.Playmode.Workflow.Editor.ClonedPlayerSystems:OnWindowChangedShouldBlock () (at ./Library/PackageCache/com.unity.multiplayer.playmode@3acaa705f0f9/Workflow/Editor/EditorSystems/ClonedPlayerSystems.cs:69)
UnityEditor.EditorWindow:OnWindowFocusChanged ()
UnityEditor.HostView:RegisterSelectedPane (bool)
UnityEditor.HostView:SetActualViewInternal (UnityEditor.EditorWindow,bool)
UnityEditor.DockArea:SetSelectedPrivate (int,bool)
UnityEditor.DockArea:AddTab (int,UnityEditor.EditorWindow,bool)
UnityEditor.DockArea:AddTab (UnityEditor.EditorWindow,bool)
UnityEditor.EditorWindow:CreateNewWindowForEditorWindow (UnityEditor.EditorWindow,bool,bool,bool)
UnityEditor.EditorWindow:Show (bool)
UnityEditor.EditorWindow:Show ()
UnityEditor.EditorWindow:GetWindowPrivate (System.Type,bool,string,bool,bool)
UnityEditor.EditorWindow:GetWindow (System.Type,bool,string,bool)
UnityEditor.EditorWindow:GetWindow<CodeMonkey.FreeWindow.MainWindowFree> (bool,string,bool)
UnityEditor.EditorWindow:GetWindow<CodeMonkey.FreeWindow.MainWindowFree> ()
CodeMonkey.FreeWindow.MainWindowFree:ShowWindow () (at C:/Users/<USER>/TicTacToe/Assets/CodeMonkeyTicTacToeFree/Editor/MainWindowFree/MainWindowFree.cs:62)
CodeMonkey.FreeWindow.MainWindowFree:Startup () (at C:/Users/<USER>/TicTacToe/Assets/CodeMonkeyTicTacToeFree/Editor/MainWindowFree/MainWindowFree.cs:42)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: ./Library/PackageCache/com.unity.multiplayer.playmode@3acaa705f0f9/Workflow/Editor/EditorSystems/ClonedPlayerSystems.cs Line: 85)

<RI> Initializing input.

Using Windows.Gaming.Input
<RI> Input initialized.

Attempted to open unsupported window in cloned player systems: CodeMonkey.FreeWindow.MainWindowFree.Please reach out on the forums and contact Unity Admins for more info.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
Unity.Multiplayer.Playmode.Workflow.Editor.ClonedPlayerSystems:BlockWindowIfRequired (UnityEditor.EditorWindow) (at ./Library/PackageCache/com.unity.multiplayer.playmode@3acaa705f0f9/Workflow/Editor/EditorSystems/ClonedPlayerSystems.cs:85)
Unity.Multiplayer.Playmode.Workflow.Editor.ClonedPlayerSystems:OnWindowChangedShouldBlock () (at ./Library/PackageCache/com.unity.multiplayer.playmode@3acaa705f0f9/Workflow/Editor/EditorSystems/ClonedPlayerSystems.cs:69)
UnityEditor.EditorWindow:OnWindowFocusChanged ()
UnityEditor.HostView:OnFocus ()
UnityEditor.ContainerWindow:Internal_BringLiveAfterCreation (bool,bool,bool)
UnityEditor.ContainerWindow:Show (UnityEditor.ShowMode,bool,bool,bool)
UnityEditor.EditorWindow:CreateNewWindowForEditorWindow (UnityEditor.EditorWindow,bool,bool,bool)
UnityEditor.EditorWindow:Show (bool)
UnityEditor.EditorWindow:Show ()
UnityEditor.EditorWindow:GetWindowPrivate (System.Type,bool,string,bool,bool)
UnityEditor.EditorWindow:GetWindow (System.Type,bool,string,bool)
UnityEditor.EditorWindow:GetWindow<CodeMonkey.FreeWindow.MainWindowFree> (bool,string,bool)
UnityEditor.EditorWindow:GetWindow<CodeMonkey.FreeWindow.MainWindowFree> ()
CodeMonkey.FreeWindow.MainWindowFree:ShowWindow () (at C:/Users/<USER>/TicTacToe/Assets/CodeMonkeyTicTacToeFree/Editor/MainWindowFree/MainWindowFree.cs:62)
CodeMonkey.FreeWindow.MainWindowFree:Startup () (at C:/Users/<USER>/TicTacToe/Assets/CodeMonkeyTicTacToeFree/Editor/MainWindowFree/MainWindowFree.cs:42)
UnityEditor.EditorApplication:Internal_CallUpdateFunctions ()

(Filename: ./Library/PackageCache/com.unity.multiplayer.playmode@3acaa705f0f9/Workflow/Editor/EditorSystems/ClonedPlayerSystems.cs Line: 85)

Input System polling thread started.
[MultiplayerPlaymode]: No Current Mode set, no window to save.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
Unity.Multiplayer.PlayMode.Common.Runtime.MppmLog:Warning (object) (at ./Library/PackageCache/com.unity.multiplayer.playmode@3acaa705f0f9/Common/Runtime/Log/MppmLog.cs:12)
Unity.Multiplayer.Playmode.Workflow.Editor.ModeSwitcher:SaveCurrentWindow () (at ./Library/PackageCache/com.unity.multiplayer.playmode@3acaa705f0f9/Workflow/Editor/EditorModes/ModeSwitcher.cs:103)
Unity.Multiplayer.Playmode.Workflow.Editor.ClonedPlayerSystems/<>c:<Listen>b__14_2 (bool) (at ./Library/PackageCache/com.unity.multiplayer.playmode@3acaa705f0f9/Workflow/Editor/EditorSystems/ClonedPlayerSystems.cs:132)
UnityEditor.EditorApplication:Internal_FocusChanged (bool)
UnityEditor.ContainerWindow:Internal_Destroy ()
UnityEditor.ContainerWindow:OnDestroy ()
UnityEngine.Object:DestroyImmediate (UnityEngine.Object,bool)
UnityEditor.ContainerWindow:Close ()
UnityEditor.SplitView:Cleanup ()
UnityEditor.DockArea:KillIfEmpty ()
UnityEditor.DockArea:RemoveTab (UnityEditor.EditorWindow,bool,bool)
UnityEditor.EditorWindow:Close ()
UnityEditor.EditorApplication:Internal_CallDelayFunctions ()

(Filename: ./Library/PackageCache/com.unity.multiplayer.playmode@3acaa705f0f9/Common/Runtime/Log/MppmLog.cs Line: 12)

Created GICache directory at C:/Users/<USER>/AppData/LocalLow/Unity/Caches/GiCache. Took: 0.015s, timestamps: [6.573 - 6.588]
[LAYOUT] About to load Library/UserSettings/Layouts\layout_0010.wlt, keepMainWindow=False
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0010.wlt
Opening scene 'Assets/Scenes/GameScene.unity'
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Loaded scene 'Assets/Scenes/GameScene.unity'
	Deserialize:            89.410 ms
	Integration:            437.142 ms
	Integration of assets:  14.876 ms
	Thread Wait Time:       0.916 ms
	Total Operation Time:   542.344 ms
Unloading 7895 unused Assets / (1.9 MB). Loaded Objects now: 8953.
Memory consumption went from 320.3 MB to 318.4 MB.
Total: 22.174500 ms (FindLiveObjects: 0.736600 ms CreateObjectMapping: 0.998600 ms MarkObjects: 18.111900 ms  DeleteObjects: 2.325900 ms)

[Licensing::Client] Successfully updated the access token
[Licensing::Module] Successfully updated access token: "QWJlryov"... (expires: 2025-10-06T00:56:09Z)
UPID Received '98b157b4-afc1-4451-8646-e67cfa6c5848'.
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0010.wlt
SpriteAtlasPacking completed in 0.003768 sec
Reloading assemblies for play mode.
Reloading assemblies after forced synchronous recompile.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.985 seconds
Refreshing native plugins compatible for Editor in 1.41 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-TicTacToe
[MODES] Loading editor mode Multiplayer Playmode Clone (1) from command line.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.812 seconds
Domain Reload Profiling: 2796ms
	BeginReloadAssembly (560ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (30ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (394ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (366ms)
		LoadAssemblies (277ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (161ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (143ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1812ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1351ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (1064ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (173ms)
Asset Pipeline Refresh (id=33a1aad703dec884a91cd803d5789656): Total: 3.449 seconds - Initiated by StopAssetImportingV2(ForceSynchronousImport | ForceDomainReload)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=2448 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=1, domain reload time=999 ms, compile time=1 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 0
	InvokeCustomDependenciesCallbacks: 0.001ms
	InvokePackagesCallback: 0.003ms
	ApplyChangesToAssetFolders: 0.315ms
	CategorizeAssets: 80.989ms
	ImportOutOfDateAssets: 1821.725ms (1817.307ms without children)
		CompileScripts: 0.946ms
		CollectScriptTypesHashes: 1.202ms
		ReloadNativeAssets: 1.100ms
		UnloadImportedAssets: 0.081ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.768ms
		InitializingProgressBar: 0.001ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.320ms
	Hotreload: 0.146ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.173ms
	UnloadStreamsBegin: 1.235ms
	UnloadStreamsEnd: 0.001ms
	GenerateScriptTypeHashes: 3.534ms
	Untracked: 1544.211ms

Loaded scene 'Temp/__Backupscenes/0.backup'
	Deserialize:            1.532 ms
	Integration:            292.194 ms
	Integration of assets:  0.002 ms
	Thread Wait Time:       0.010 ms
	Total Operation Time:   293.739 ms
Asset Pipeline Refresh (id=1d33bdaf410d4d64bb1af4194805e7d6): Total: 0.495 seconds - Initiated by RefreshV2(ForceUpdate)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=495 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=0, domain reload time=0 ms, compile time=0 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeCustomDependenciesCallbacks: 0.000ms
	InvokePackagesCallback: 0.001ms
	ApplyChangesToAssetFolders: 0.242ms
	CategorizeAssets: 76.992ms
	ImportOutOfDateAssets: 1.226ms (0.249ms without children)
		ReloadNativeAssets: 0.027ms
		UnloadImportedAssets: 0.001ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.537ms
		InitializingProgressBar: 0.001ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.411ms
	Hotreload: 1.030ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.238ms
	UnloadStreamsBegin: 0.135ms
	UnloadStreamsEnd: 0.002ms
	Untracked: 415.450ms

[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0010.wlt
[LAYOUT] About to load Library/UserSettings/Layouts\layout_0031.wlt, keepMainWindow=False
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
TrimDiskCacheJob: Current cache size 0mb
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Loaded scene 'Temp/__Backupscenes/0.backup'
	Deserialize:            1.187 ms
	Integration:            215.205 ms
	Integration of assets:  0.001 ms
	Thread Wait Time:       0.011 ms
	Total Operation Time:   216.404 ms
Unloading 7907 unused Assets / (6.0 MB). Loaded Objects now: 9914.
Memory consumption went from 416.0 MB to 410.0 MB.
Total: 62.675700 ms (FindLiveObjects: 0.712900 ms CreateObjectMapping: 0.585200 ms MarkObjects: 58.730700 ms  DeleteObjects: 2.645700 ms)

Asset Pipeline Refresh (id=419801642a724c5488c10587b023bb46): Total: 0.377 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=377 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=0, domain reload time=0 ms, compile time=0 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeCustomDependenciesCallbacks: 0.000ms
	InvokePackagesCallback: 0.001ms
	ApplyChangesToAssetFolders: 0.205ms
	CategorizeAssets: 57.173ms
	ImportOutOfDateAssets: 0.963ms (0.236ms without children)
		ReloadNativeAssets: 0.020ms
		UnloadImportedAssets: 0.001ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.385ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.320ms
	Hotreload: 1.037ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.220ms
	UnloadStreamsBegin: 0.044ms
	UnloadStreamsEnd: 0.001ms
	Untracked: 317.453ms

[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
[LAYOUT] About to load Library/UserSettings/Layouts\layout_0010.wlt, keepMainWindow=False
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0010.wlt
SpriteAtlasPacking completed in 0.003227 sec
Reloading assemblies for play mode.
Reloading assemblies after forced synchronous recompile.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.991 seconds
Refreshing native plugins compatible for Editor in 1.51 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-TicTacToe
[MODES] Loading editor mode Multiplayer Playmode Clone (1) from command line.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.840 seconds
Domain Reload Profiling: 2831ms
	BeginReloadAssembly (561ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (26ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (390ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (371ms)
		LoadAssemblies (270ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (174ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (155ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1840ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1277ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (165ms)
			ProcessInitializeOnLoadAttributes (991ms)
			ProcessInitializeOnLoadMethodAttributes (90ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (214ms)
Asset Pipeline Refresh (id=26b6ea6698a192e4d97dedef02264edf): Total: 3.492 seconds - Initiated by StopAssetImportingV2(ForceSynchronousImport | ForceDomainReload)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=2484 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=1, domain reload time=1005 ms, compile time=1 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 0
	InvokeCustomDependenciesCallbacks: 0.000ms
	InvokePackagesCallback: 0.003ms
	ApplyChangesToAssetFolders: 0.339ms
	CategorizeAssets: 90.336ms
	ImportOutOfDateAssets: 1850.301ms (1846.757ms without children)
		CompileScripts: 0.967ms
		CollectScriptTypesHashes: 0.617ms
		ReloadNativeAssets: 0.776ms
		UnloadImportedAssets: 0.091ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.677ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.414ms
	Hotreload: 0.142ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.197ms
	UnloadStreamsBegin: 0.002ms
	UnloadStreamsEnd: 0.002ms
	GenerateScriptTypeHashes: 3.085ms
	Untracked: 1550.236ms

Loaded scene 'Temp/__Backupscenes/0.backup'
	Deserialize:            1.534 ms
	Integration:            274.954 ms
	Integration of assets:  0.002 ms
	Thread Wait Time:       0.017 ms
	Total Operation Time:   276.506 ms
Asset Pipeline Refresh (id=96d2658bbb272c047b4b9ee315987541): Total: 0.495 seconds - Initiated by RefreshV2(ForceUpdate)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=495 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=0, domain reload time=0 ms, compile time=0 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeCustomDependenciesCallbacks: 0.000ms
	InvokePackagesCallback: 0.001ms
	ApplyChangesToAssetFolders: 0.206ms
	CategorizeAssets: 76.610ms
	ImportOutOfDateAssets: 1.079ms (0.262ms without children)
		ReloadNativeAssets: 0.032ms
		UnloadImportedAssets: 0.001ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.451ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.332ms
	Hotreload: 1.232ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.192ms
	UnloadStreamsBegin: 0.003ms
	UnloadStreamsEnd: 0.001ms
	Untracked: 415.926ms

[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0010.wlt
[LAYOUT] About to load Library/UserSettings/Layouts\layout_0031.wlt, keepMainWindow=False
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Loaded scene 'Temp/__Backupscenes/0.backup'
	Deserialize:            1.165 ms
	Integration:            211.109 ms
	Integration of assets:  0.002 ms
	Thread Wait Time:       0.009 ms
	Total Operation Time:   212.285 ms
Unloading 7897 unused Assets / (4.4 MB). Loaded Objects now: 9918.
Memory consumption went from 411.3 MB to 406.9 MB.
Total: 67.997600 ms (FindLiveObjects: 0.660200 ms CreateObjectMapping: 0.656200 ms MarkObjects: 64.141100 ms  DeleteObjects: 2.539100 ms)

[Licensing::Client] Successfully resolved entitlement details
Asset Pipeline Refresh (id=ee2b7690a0b8ef549930911d6d3a3407): Total: 0.403 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=402 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=0, domain reload time=0 ms, compile time=0 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeCustomDependenciesCallbacks: 0.001ms
	InvokePackagesCallback: 0.001ms
	ApplyChangesToAssetFolders: 0.264ms
	CategorizeAssets: 58.828ms
	ImportOutOfDateAssets: 1.122ms (0.232ms without children)
		ReloadNativeAssets: 0.019ms
		UnloadImportedAssets: 0.001ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.389ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.480ms
	Hotreload: 1.977ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.221ms
	UnloadStreamsBegin: 0.003ms
	UnloadStreamsEnd: 0.001ms
	Untracked: 340.196ms

[LAYOUT] About to load Library/UserSettings/Layouts\layout_0010.wlt, keepMainWindow=False
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0010.wlt
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0010.wlt
Reloading assemblies due to reload request.
Reloading assemblies after forced synchronous recompile.
[Licensing::Client] Successfully resolved entitlement details
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.240 seconds
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-TicTacToe
[MODES] Loading editor mode Multiplayer Playmode Clone (1) from command line.
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.931 seconds
Domain Reload Profiling: 3170ms
	BeginReloadAssembly (724ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (55ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (487ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (446ms)
		LoadAssemblies (321ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (205ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (180ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1931ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1409ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (240ms)
			ProcessInitializeOnLoadAttributes (1035ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (110ms)
Asset Pipeline Refresh (id=3a75b199d0131a247a72d6f0434cb4b5): Total: 3.905 seconds - Initiated by StopAssetImportingV2(ForceSynchronousImport | ForceDomainReload)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=2643 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=1, domain reload time=1260 ms, compile time=1 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 0
	InvokeCustomDependenciesCallbacks: 0.001ms
	InvokePackagesCallback: 0.004ms
	ApplyChangesToAssetFolders: 0.478ms
	CategorizeAssets: 82.174ms
	ImportOutOfDateAssets: 1941.945ms (1938.095ms without children)
		CompileScripts: 1.047ms
		CollectScriptTypesHashes: 0.762ms
		ReloadNativeAssets: 0.821ms
		UnloadImportedAssets: 0.097ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.692ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.429ms
	Hotreload: 1.611ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.286ms
	UnloadStreamsBegin: 0.045ms
	UnloadStreamsEnd: 0.001ms
	GenerateScriptTypeHashes: 2.885ms
	Untracked: 1878.459ms

SpriteAtlasPacking completed in 0.003251 sec
Reloading assemblies for play mode.
Reloading assemblies after forced synchronous recompile.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.024 seconds
Refreshing native plugins compatible for Editor in 1.57 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-TicTacToe
[MODES] Loading editor mode Multiplayer Playmode Clone (1) from command line.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.872 seconds
Domain Reload Profiling: 2896ms
	BeginReloadAssembly (569ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (67ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (355ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (389ms)
		LoadAssemblies (280ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (181ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (162ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1872ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1063ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (801ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (145ms)
Asset Pipeline Refresh (id=8d80f375a3427a448a77b1fb42ba42a4): Total: 3.635 seconds - Initiated by StopAssetImportingV2(ForceSynchronousImport | ForceDomainReload)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=2594 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=1, domain reload time=1039 ms, compile time=1 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 0
	InvokeCustomDependenciesCallbacks: 0.001ms
	InvokePackagesCallback: 0.004ms
	ApplyChangesToAssetFolders: 0.384ms
	CategorizeAssets: 90.519ms
	ImportOutOfDateAssets: 1882.727ms (1878.059ms without children)
		CompileScripts: 0.996ms
		CollectScriptTypesHashes: 1.866ms
		ReloadNativeAssets: 0.815ms
		UnloadImportedAssets: 0.002ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.675ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.313ms
	Hotreload: 4.609ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.225ms
	UnloadStreamsBegin: 0.003ms
	UnloadStreamsEnd: 0.001ms
	GenerateScriptTypeHashes: 2.997ms
	Untracked: 1656.935ms

Loaded scene 'Temp/__Backupscenes/0.backup'
	Deserialize:            1.588 ms
	Integration:            374.312 ms
	Integration of assets:  0.002 ms
	Thread Wait Time:       0.019 ms
	Total Operation Time:   375.920 ms
Asset Pipeline Refresh (id=42c167dded9da994d930223c39d30579): Total: 0.500 seconds - Initiated by RefreshV2(ForceUpdate)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=499 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=0, domain reload time=0 ms, compile time=0 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeCustomDependenciesCallbacks: 0.001ms
	InvokePackagesCallback: 0.001ms
	ApplyChangesToAssetFolders: 0.241ms
	CategorizeAssets: 76.177ms
	ImportOutOfDateAssets: 1.175ms (0.318ms without children)
		ReloadNativeAssets: 0.027ms
		UnloadImportedAssets: 0.001ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.447ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.381ms
	Hotreload: 2.736ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.266ms
	UnloadStreamsBegin: 0.002ms
	UnloadStreamsEnd: 0.002ms
	Untracked: 419.208ms

[LAYOUT] About to load Library/UserSettings/Layouts\layout_0031.wlt, keepMainWindow=False
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Loaded scene 'Temp/__Backupscenes/0.backup'
	Deserialize:            1.219 ms
	Integration:            218.947 ms
	Integration of assets:  0.001 ms
	Thread Wait Time:       0.013 ms
	Total Operation Time:   220.181 ms
Unloading 7895 unused Assets / (4.4 MB). Loaded Objects now: 10032.
Memory consumption went from 419.2 MB to 414.8 MB.
Total: 60.927100 ms (FindLiveObjects: 0.681900 ms CreateObjectMapping: 0.629500 ms MarkObjects: 56.861100 ms  DeleteObjects: 2.753300 ms)

[Licensing::Client] Successfully resolved entitlement details
Asset Pipeline Refresh (id=f6b21ed5c1ccc434287356612119f782): Total: 0.412 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=411 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=0, domain reload time=0 ms, compile time=0 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeCustomDependenciesCallbacks: 0.001ms
	InvokePackagesCallback: 0.001ms
	ApplyChangesToAssetFolders: 0.212ms
	CategorizeAssets: 61.527ms
	ImportOutOfDateAssets: 1.034ms (0.252ms without children)
		ReloadNativeAssets: 0.025ms
		UnloadImportedAssets: 0.002ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.449ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.306ms
	Hotreload: 1.185ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.163ms
	UnloadStreamsBegin: 0.048ms
	UnloadStreamsEnd: 0.001ms
	Untracked: 347.443ms

[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
[LAYOUT] About to load Library/UserSettings/Layouts\layout_0010.wlt, keepMainWindow=False
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0010.wlt
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0010.wlt
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0010.wlt
Reloading assemblies due to reload request.
Reloading assemblies after forced synchronous recompile.
[Licensing::Client] Successfully resolved entitlement details
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.194 seconds
Refreshing native plugins compatible for Editor in 1.55 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-TicTacToe
[MODES] Loading editor mode Multiplayer Playmode Clone (1) from command line.
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.812 seconds
Domain Reload Profiling: 3006ms
	BeginReloadAssembly (696ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (31ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (486ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (429ms)
		LoadAssemblies (310ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (195ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (171ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1813ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1310ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (219ms)
			ProcessInitializeOnLoadAttributes (970ms)
			ProcessInitializeOnLoadMethodAttributes (89ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (102ms)
Asset Pipeline Refresh (id=1a4335cedf6f65746835f7890f628a4e): Total: 3.682 seconds - Initiated by StopAssetImportingV2(ForceSynchronousImport | ForceDomainReload)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=2466 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=1, domain reload time=1214 ms, compile time=1 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 0
	InvokeCustomDependenciesCallbacks: 0.001ms
	InvokePackagesCallback: 0.004ms
	ApplyChangesToAssetFolders: 0.482ms
	CategorizeAssets: 76.255ms
	ImportOutOfDateAssets: 1822.058ms (1818.420ms without children)
		CompileScripts: 1.001ms
		CollectScriptTypesHashes: 0.766ms
		ReloadNativeAssets: 0.757ms
		UnloadImportedAssets: 0.092ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.609ms
		InitializingProgressBar: 0.001ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.412ms
	Hotreload: 1.410ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.296ms
	UnloadStreamsBegin: 0.002ms
	UnloadStreamsEnd: 0.001ms
	GenerateScriptTypeHashes: 2.533ms
	Untracked: 1781.023ms

