{"version": 3, "targets": {".NETStandard,Version=v2.1": {"CodeMonkeyFree/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/CodeMonkeyFree.dll": {}}, "runtime": {"bin/placeholder/CodeMonkeyFree.dll": {}}}}}, "libraries": {"CodeMonkeyFree/1.0.0": {"type": "project", "path": "CodeMonkeyFree.csproj", "msbuildProject": "CodeMonkeyFree.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["CodeMonkeyFree >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\TicTacToe\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "C:\\Users\\<USER>\\TicTacToe\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\TicTacToe\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\TicTacToe\\CodeMonkeyFree.csproj": {"projectPath": "C:\\Users\\<USER>\\TicTacToe\\CodeMonkeyFree.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.305\\RuntimeIdentifierGraph.json"}}}}