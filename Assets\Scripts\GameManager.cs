using UnityEngine;
using System;
using Unity.Netcode;
public class GameManager : NetworkBehaviour
{

    public static GameManager Instance { get; private set; }

    public event EventHandler<OnClickedOnGridPositionEventArgs> OnClickedOnGridPosition;
    public class OnClickedOnGridPositionEventArgs : EventArgs
    {
        public int x;
        public int y;
        public PlayerType playerType;

    }

    private PlayerType localPlayerType;
    public enum PlayerType { None, Cross, Circle };


    private void Awake()
    {
        if (Instance != null)
        {
            Debug.LogError("Multiple GameManager instances found!");
            return;
        }
        Instance = this;
    }

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();
        if (IsServer)
        {
            localPlayerType = PlayerType.Cross;
        }
        else if (IsClient)
        {
            localPlayerType = PlayerType.Circle;
        }
    }

    public void ClickedOnGridPosition(int x, int y)
    {
        OnClickedOnGridPosition?.Invoke(this, new OnClickedOnGridPositionEventArgs
        {
            x = x,
            y = y,
            playerType = GetLocalPlayerType()
        });
    }
    
    public PlayerType GetLocalPlayerType()
    {
        return localPlayerType;
    }

}
