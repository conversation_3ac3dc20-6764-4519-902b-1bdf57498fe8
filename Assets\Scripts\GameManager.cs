using UnityEngine;
using System;
using Unity.Netcode;
public class GameManager : NetworkBehaviour
{

    public static GameManager Instance { get; private set; }

    public event EventHandler<OnClickedOnGridPositionEventArgs> OnClickedOnGridPosition;
    public class OnClickedOnGridPositionEventArgs : EventArgs
    {
        public int x;
        public int y;
        public PlayerType playerType;

    }

    private PlayerType localPlayerType;
    public enum PlayerType { Cross, Circle };


    private void Awake()
    {
        if (Instance != null)
        {
            Debug.LogError("Multiple GameManager instances found!");
            return;
        }
        Instance = this;
    }

    public override void OnNetworkSpawn()
    {
        if (NetworkManager.Singleton.LocalClientId == 0)
        {
            localPlayerType = PlayerType.Cross;
        }
        else
        {
            localPlayerType = PlayerType.Circle;
        }
    }

    public void ClickedOnGridPosition(int x, int y)
    {
        PlayerType currentPlayerType = GetLocalPlayerType();

        OnClickedOnGridPosition?.Invoke(this, new OnClickedOnGridPositionEventArgs
        {
            x = x,
            y = y,
            playerType = currentPlayerType
        });
    }
    
    public PlayerType GetLocalPlayerType()
    {
        return localPlayerType;
    }

}
