{"format": 1, "restore": {"C:\\Users\\<USER>\\TicTacToe\\CodeMonkeyFree.csproj": {}}, "projects": {"C:\\Users\\<USER>\\TicTacToe\\CodeMonkeyFree.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\TicTacToe\\CodeMonkeyFree.csproj", "projectName": "CodeMonkeyFree", "projectPath": "C:\\Users\\<USER>\\TicTacToe\\CodeMonkeyFree.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\TicTacToe\\Temp\\obj\\CodeMonkeyFree\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.305\\RuntimeIdentifierGraph.json"}}}}}